import { Navigate, Route, Routes } from "react-router";
import GlobalSigninPage from "./pages/global-signin-page";
import { useContext } from "react";
import { globalContext } from "./global-context";
import GlobalLayout from "./components/layout/global-layout";
import TestPage from "./pages/test-page";
import ExternalLayout from "./components/layout/external-layout";
import ExternalDashboardPage from "./pages/external-dashboard-page";
import ExternalMapPage from "./pages/external-map-page";

function App() {
  const { loged } = useContext(globalContext);
  return (
    <Routes>
      <Route>
        <Route path="*" element={<Navigate to={"/"} />} />
        {loged ? (
          <Route>
            <Route path="test" element={<TestPage />} />
            <Route element={<GlobalLayout />}>
              <Route element={<ExternalLayout />}>
                <Route path="" element={<ExternalDashboardPage />} />
                <Route path="map" element={<ExternalMapPage />} />
              </Route>
            </Route>
          </Route>
        ) : (
          <Route>
            <Route path="" element={"Landing"} />
            <Route path="signin" element={<GlobalSigninPage />} />
          </Route>
        )}
      </Route>
    </Routes>
  );
}

export default App;
