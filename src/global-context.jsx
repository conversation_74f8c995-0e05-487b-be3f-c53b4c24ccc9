/* eslint-disable react-refresh/only-export-components */
import { onAuthStateChanged } from "firebase/auth";
import { createContext, useState } from "react";
import { auth } from "./services/firebase";

export const globalContext = createContext();

export default function GlobalContext(props) {
  const [loged, setLoged] = useState(null);

  onAuthStateChanged(auth, (u) => {
    if (u) {
      setLoged(true);
    }
    if (!u) {
      setLoged(false);
    }
  });

  if (loged === null) return null;
  return (
    <globalContext.Provider value={{ loged }}>
      {props.children}
    </globalContext.Provider>
  );
}
