import { Colors } from "@blueprintjs/core";
import dayjs from "dayjs";
import React, { useEffect, useRef } from "react";
import { useState } from "react";
import { useSearchParams } from "react-router";
import { DataSet } from "vis-data";
import { Timeline } from "vis-timeline/standalone";
import "vis-timeline/styles/vis-timeline-graph2d.min.css";

export default function Test() {
  const timelineRef = useRef(null);
  const [userTime, setUserTime] = useState();
  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    const container = timelineRef.current;

    const items = new DataSet([]);

    const options = {
      editable: false,
      align: "center",
      showWeekScale: true,
      start: dayjs().startOf("day").toDate(),
      end: dayjs().endOf("day").toDate(),
    };

    const timeline = new Timeline(container, items, options);
    timeline.addCustomTime(dayjs().toDate(), "id");
    timeline.on("timechange", (e) =>
      setSearchParams({ time: dayjs(e.time).unix() })
    );

    return () => timeline.destroy(); // clean up
  }, []);

  return (
    <div
      style={{
        display: "flex",
        flexFlow: "column",
        minHeight: "100svh",
      }}
    >
      <div style={{ flex: 1 }}>
        {dayjs(searchParams.get("time") * 1000).format(
          "ddd DD MMMM YYYY HH:mm:ss"
        )}
      </div>
      <div>
        <div
          ref={timelineRef}
          style={{
            height: "auto",
            backgroundColor: Colors.LIGHT_GRAY5,
            borderTop: `1px solid ${Colors.LIGHT_GRAY1}`,
            paddingBlock: 10,
          }}
        />
      </div>
    </div>
  );
}
