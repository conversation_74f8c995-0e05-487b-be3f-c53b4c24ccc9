import { Card, Colors, EntityTitle, H2 } from "@blueprintjs/core";
import PageTitleUi from "../components/elements/page-title-ui";
import { useOutletContext } from "react-router";
import { useEffect } from "react";

export default function ExternalDashboardPage() {
  const { sites, activities, issues } = useOutletContext();

  return (
    <>
      <PageTitleUi title={"Dashboard"} actions={<></>} />
      <div
        style={{
          paddingInline: "40px 20px",
        }}
      >
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            columnGap: 15,
            rowGap: 15,
          }}
        >
          <Card compact>
            <EntityTitle
              heading={H2}
              title={sites?.length}
              subtitle={"SITES"}
            />
          </Card>
          <Card compact>
            <EntityTitle
              heading={H2}
              title={
                (sites.filter((s) => s.state === "Operational").length /
                  sites?.length) *
                  100 +
                "%"
              }
              subtitle={"OPERATIONAL"}
            />
          </Card>
          <Card compact>
            <EntityTitle
              heading={H2}
              title={activities?.length}
              subtitle={"ACTIVITIES"}
            />
          </Card>
          <Card compact>
            <EntityTitle
              heading={H2}
              title={issues?.length}
              subtitle={"ISSUES"}
            />
          </Card>
        </div>
      </div>
      <div style={{ flex: 1 }}></div>
      <div>DASH</div>
    </>
  );
}
