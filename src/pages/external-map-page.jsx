import { Colors, EntityTitle } from "@blueprintjs/core";
import { useEffect, useRef, useState } from "react";
import Map from "react-map-gl/mapbox";
import { useOutletContext, useSearchParams } from "react-router";
import ExternalControlMap from "../components/map/controls/external-control-map";
import tz_lookup from "tz-lookup";
import TileDayNight from "../components/map/layout/tile-day-night";
import TimelineUi from "../components/elements/timeline-ui";
import dayjs from "dayjs";

export default function ExternalMapPage() {
  const { sidebarOpen, theme } = useOutletContext();
  const [searchParams, setSearchParams] = useSearchParams();
  const [viewState, setViewState] = useState();
  const [center, setCenter] = useState();
  const [timezone, setTimezone] = useState();
  const [mapStyle, setMapStyle] = useState(
    theme === "light"
      ? "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
      : "mapbox://styles/tsinovec/cmdrfkyi5008801r5ckbg9zvf"
  );
  const mapRef = useRef();

  useEffect(() => {
    if (mapRef.current) {
      mapRef.current.resize();
    }
  }, [sidebarOpen]);

  useEffect(() => {
    setMapStyle(
      theme === "light"
        ? "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
        : "mapbox://styles/tsinovec/cmdrfkyi5008801r5ckbg9zvf"
    );
  }, [theme]);

  return (
    <>
      <div
        style={{
          flex: 1,
          display: "flex",
          borderTop: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
          }`,
        }}
      >
        <div style={{ flex: 1, display: "flex", flexFlow: "column" }}>
          <Map
            mapStyle={mapStyle}
            ref={mapRef}
            style={{ flex: 1 }}
            attributionControl={false}
            terrain={{ source: "mapbox-dem", exaggeration: 1 }}
            minZoom={1}
            projection={"mercator"}
            onMove={(event) => {
              setViewState({ ...viewState, zoom: event.viewState.zoom });
            }}
            onMouseMove={(event) => {
              setTimezone(tz_lookup(event.lngLat.lat, event.lngLat.lng));

              setViewState({
                latitude: event.lngLat.lat,
                longitude: event.lngLat.lng,
                zoom: event.target.getZoom(),
              });
            }}
            mapboxAccessToken={import.meta.env.VITE_MAPBOX_TOKEN}
          >
            <ExternalControlMap mapRef={mapRef} />
            <TileDayNight
              date={
                searchParams.get("time")
                  ? dayjs(searchParams.get("time") * 1000).toDate()
                  : dayjs().toDate()
              }
            />
          </Map>
          <div
            style={{
              backgroundColor:
                theme === "light" ? Colors.LIGHT_GRAY5 : Colors.DARK_GRAY2,
              height: 79.8,
            }}
          >
            <TimelineUi />
          </div>
        </div>
        <div
          style={{
            display: "flex",
            flexFlow: "column",
            width: 300,
            borderLeft: `1px solid ${
              theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
            }`,
          }}
        >
          <div style={{ flex: 1 }}></div>
          <div
            style={{
              padding: 20,
              display: "flex",
              justifyContent: "space-between",
              backgroundColor:
                theme === "light" ? Colors.LIGHT_GRAY5 : Colors.DARK_GRAY2,
              borderTop: `1px solid ${
                theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
              }`,
              fontFamily: "monospace",
              fontSize: 12,
              height: 79.8,
            }}
          >
            <EntityTitle
              title={new Date().toLocaleTimeString("en-US", {
                timeZone: timezone,
                hour12: false,
                hour: "numeric",
                minute: "numeric",
              })}
              subtitle={"LOCAL TIME"}
            />
            <EntityTitle
              title={viewState?.latitude.toPrecision(5) || 0.0}
              subtitle={"LATITUDE"}
            />
            <EntityTitle
              title={viewState?.longitude.toPrecision(5) || 0.0}
              subtitle={"LONGITUDE"}
            />
            <EntityTitle
              title={viewState?.zoom.toPrecision(2) || 0.0}
              subtitle={"ZOOM"}
            />
          </div>
        </div>
      </div>
    </>
  );
}
