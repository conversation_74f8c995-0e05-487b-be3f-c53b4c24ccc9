import { Button, Colors, FormGroup, InputGroup } from "@blueprintjs/core";
import { functions } from "../services/functions";

export default function GlobalSigninPage() {
  return (
    <>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "100svh",
          backgroundColor: Colors.LIGHT_GRAY5,
        }}
      >
        <div style={{ width: 300 }}>
          <div style={{ marginBottom: 30 }}>
            <div style={{ fontWeight: 700, fontSize: 20 }}>Welcome back!</div>
            <div>Add your credentials to sign in to FMAIP</div>
          </div>
          <form onSubmit={(event) => functions.auth.signin(event)}>
            <FormGroup label="Email address">
              <InputGroup name="email" required type="email" />
            </FormGroup>
            <FormGroup
              label="Password"
              helperText={
                <div style={{ textAlign: "right" }}>
                  <span style={{ cursor: "pointer" }}>Forgot password?</span>
                </div>
              }
            >
              <InputGroup name="password" required type="password" />
            </FormGroup>
            <Button
              type="submit"
              text="Sign in"
              fill
              intent="primary"
              style={{ marginTop: 30 }}
            />
          </form>
        </div>
      </div>
    </>
  );
}
