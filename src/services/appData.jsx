import { collection, onSnapshot, query, where } from "firebase/firestore";
import { useEffect, useState } from "react";
import { auth, db } from "./firebase";

export const appData = {
  external: {
    sites: {
      ListAll: () => {
        const [sites, setSites] = useState([]);
        useEffect(() => {
          onSnapshot(
            query(
              collection(db, "sites"),
              where("members", "array-contains", auth.currentUser.uid)
            ),
            (snapOnSites) =>
              setSites(
                snapOnSites.docs.map((site) => ({
                  id: site.id,
                  ...site.data(),
                }))
              )
          );
        }, []);

        return sites;
      },
    },
    activities: {
      ListAll: (sites) => {
        const [activities, setActivities] = useState([]);

        useEffect(() => {
          if (!sites || sites.length === 0) return;

          const batchSize = 30;
          const unsubscribes = [];

          const allActivities = new Map(); // pour éviter les doublons et gérer l'état proprement

          const updateState = () => {
            setActivities(Array.from(allActivities.values()));
          };

          for (let i = 0; i < sites.length; i += batchSize) {
            const batch = sites.slice(i, i + batchSize).map((s) => s.id);

            const unsubscribe = onSnapshot(
              query(collection(db, "activities"), where("site", "in", batch)),
              (querySnapshot) => {
                // Mettre à jour les entrées dans le Map
                querySnapshot.docs.forEach((doc) => {
                  allActivities.set(doc.id, {
                    id: doc.id,
                    ...doc.data(),
                  });
                });
                updateState();
              }
            );

            unsubscribes.push(unsubscribe);
          }

          // Nettoyage des listeners
          return () => {
            unsubscribes.forEach((unsub) => unsub());
          };
        }, [sites]);

        return activities;
      },
    },
    issues: {
      ListAll: (sites) => {
        const [issues, setIssues] = useState([]);

        useEffect(() => {
          if (!sites || sites.length === 0) return;

          const batchSize = 30;
          const unsubscribes = [];

          const allIssues = new Map(); // pour éviter les doublons et gérer l'état proprement

          const updateState = () => {
            setIssues(Array.from(allIssues.values()));
          };

          for (let i = 0; i < sites.length; i += batchSize) {
            const batch = sites.slice(i, i + batchSize).map((s) => s.id);

            const unsubscribe = onSnapshot(
              query(collection(db, "issues"), where("site", "in", batch)),
              (querySnapshot) => {
                // Mettre à jour les entrées dans le Map
                querySnapshot.docs.forEach((doc) => {
                  allIssues.set(doc.id, {
                    id: doc.id,
                    ...doc.data(),
                  });
                });
                updateState();
              }
            );

            unsubscribes.push(unsubscribe);
          }

          // Nettoyage des listeners
          return () => {
            unsubscribes.forEach((unsub) => unsub());
          };
        }, [sites]);

        return issues;
      },
    },
  },
};
