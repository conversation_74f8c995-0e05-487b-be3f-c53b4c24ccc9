import { signInWithEmailAndPassword } from "firebase/auth";
import { auth, db } from "./firebase";
import { OverlayToaster } from "@blueprintjs/core";
import {
  addDoc,
  arrayUnion,
  collection,
  doc,
  updateDoc,
} from "firebase/firestore";
import dayjs from "dayjs";
import { useEffect, useState } from "react";

export const functions = {
  auth: {
    signin: async (event) => {
      event.preventDefault();
      const toaster = await OverlayToaster.create();
      signInWithEmailAndPassword(
        auth,
        event.target.email.value,
        event.target.password.value
      )
        .then((user) => {
          fetch("https://api64.ipify.org?format=json")
            .then((respons) => respons.json())
            .then((result) => {
              addDoc(collection(db, "logs"), {
                type: "auth",
                message: `User logged in from IP ${
                  result.ip
                } on ${dayjs().format("ddd DD MMMM YYYY HH:mm:ss")} from ${
                  navigator.userAgent
                }`,
                created: dayjs().toDate(),
                metadata: {
                  ip: result.ip,
                  userEmail: user.user.email,
                },
                userUid: user.user.uid,
              });

              updateDoc(doc(db, "users", user.user.uid), {
                devices: arrayUnion({
                  ip: result.ip,
                  navigator: navigator.userAgent,
                  created: dayjs().toDate(),
                }),
              });
            });
        })
        .catch((error) => {
          toaster.show({
            icon: "error",
            intent: "danger",
            message: error.code,
          });
        });
    },
  },
  api: {
    openSky: {
      states: {
        AllFree: () => {
          const [flights, setFlights] = useState([]);

          useEffect(() => {
            fetch("https://opensky-network.org/api/states/all")
              .then((response) => response.json())
              .then((result) => setFlights(result));
          }, []);
          return flights;
        },
      },
    },
  },
};
