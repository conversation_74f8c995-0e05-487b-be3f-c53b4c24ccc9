import { Colors, Menu, MenuItem, Tag } from "@blueprintjs/core";
import { Outlet, useNavigate, useOutletContext } from "react-router";
import { appData } from "../../services/appData";
import { functions } from "../../services/functions";

export default function ExternalLayout() {
  const { sidebarOpen, theme } = useOutletContext();
  const navigate = useNavigate();

  const sites = appData.external.sites.ListAll();
  const activities = appData.external.activities.ListAll(sites);
  const issues = appData.external.issues.ListAll(sites);
  const flights = functions.api.openSky.states.AllFree();

  return (
    <>
      <div
        style={{
          width: sidebarOpen ? 230 : 50,
          borderRight: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
          }`,
          paddingBlock: 10,
          overflow: "hidden",
        }}
      >
        <Menu
          style={{ padding: 0, backgroundColor: "transparent" }}
          className="sidebarMenu"
        >
          <MenuItem
            title="Dashboard"
            text={sidebarOpen && "Dashboard"}
            icon="dashboard"
            intent={window.location.pathname === "/" ? "primary" : "none"}
            onClick={() => navigate("")}
          />
          <MenuItem
            title="Map view"
            text={sidebarOpen && "Map view"}
            icon="map"
            intent={window.location.pathname === "/map" ? "primary" : "none"}
            onClick={() => navigate("map")}
          />
        </Menu>
      </div>
      <div
        style={{
          flex: 1,
          overflow: "auto",
          display: "flex",
          flexFlow: "column",
        }}
      >
        <Outlet
          context={{
            sidebarOpen,
            theme,
            sites,
            activities,
            issues,
            flights,
          }}
        />
      </div>
    </>
  );
}
