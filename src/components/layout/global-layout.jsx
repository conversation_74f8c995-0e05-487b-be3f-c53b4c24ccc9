import {
  Button,
  Colors,
  CompoundTag,
  EntityT<PERSON>le,
  <PERSON>u,
  MenuDivider,
  MenuItem,
  Popover,
} from "@blueprintjs/core";
import { doc, getDoc } from "firebase/firestore";
import { auth, db } from "../../services/firebase";
import { useEffect, useState } from "react";
import { Outlet } from "react-router";
import dayjs from "dayjs";
import Logo from "../assets/logo";

export default function GlobalLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [theme, setTheme] = useState("light");
  const [user, setUser] = useState();

  useEffect(() => {
    getDoc(doc(db, "users", auth.currentUser.uid)).then((doc) => {
      setUser(doc.data());
    });
  }, []);

  return (
    <>
      <div
        style={{
          display: "flex",
          overflow: "auto",
          flexFlow: "column",
          minHeight: "100svh",
          maxHeight: "100svh",
          backgroundColor: theme === "light" ? "white" : Colors.DARK_GRAY1,
        }}
        className={theme === "light" ? null : "bp6-dark"}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div
            style={{
              height: 50,
              width: sidebarOpen ? 230 : 50,
              display: "flex",
              alignItems: "center",
              paddingInline: 15,
              borderRight: `1px solid ${
                theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
              }`,
              justifyContent: "space-between",
            }}
          >
            {sidebarOpen && <Logo />}
            <Button
              icon={sidebarOpen ? "menu-closed" : "menu-open"}
              size="small"
              variant="minimal"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            />
          </div>
          <div
            style={{
              flex: 1,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              paddingInline: "40px 15px",
              height: 50,
            }}
          >
            <div>{dayjs().format("dddd DD MMMM YYYY").toLocaleUpperCase()}</div>
            <div style={{ display: "flex" }}>
              <Button
                icon={theme === "light" ? "moon" : "flash"}
                variant="minimal"
                onClick={() => setTheme(theme === "light" ? "dark" : "light")}
              />
              <Button icon="cog" variant="minimal" />
              <Button icon="notifications" variant="minimal" />
              <Popover
                placement="bottom-end"
                content={
                  <Menu style={{ minWidth: 230 }}>
                    <MenuDivider
                      title={
                        <EntityTitle
                          title={`${user?.firstName} ${user?.lastName}`}
                          subtitle={auth.currentUser.email}
                          fill
                          tags={
                            <CompoundTag leftContent={user?.role} minimal>
                              {user?.type}
                            </CompoundTag>
                          }
                        />
                      }
                    />
                    <MenuDivider />
                    <MenuItem text="Account settings" icon="user" />
                    <MenuItem text="Updtates" icon="updated" />
                    <MenuItem text="Documentation" icon="book" />
                    <MenuItem text="Support" icon="help" />
                    <MenuDivider />
                    {user?.type === "internal" && (
                      <MenuItem
                        text="Internal panel"
                        icon="open-application"
                        onClick={() => window.open("/internal", "_blank")}
                      />
                    )}
                    <MenuItem
                      text="Log out"
                      icon="log-out"
                      intent="danger"
                      onClick={() => auth.signOut()}
                    />
                  </Menu>
                }
              >
                <Button icon="user" variant="minimal" />
              </Popover>
            </div>
          </div>
        </div>
        <div
          style={{
            flex: 1,
            display: "flex",
            overflow: "auto",
          }}
        >
          <Outlet context={{ sidebarOpen, theme }} />
        </div>
      </div>
    </>
  );
}
