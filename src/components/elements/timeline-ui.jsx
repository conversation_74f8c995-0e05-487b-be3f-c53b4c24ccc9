import { Colors } from "@blueprintjs/core";
import dayjs from "dayjs";
import React, { useEffect, useRef } from "react";
import { useState } from "react";
import { useOutletContext, useSearchParams } from "react-router";
import { DataSet } from "vis-data";
import { Timeline } from "vis-timeline/standalone";
import "vis-timeline/styles/vis-timeline-graph2d.min.css";

export default function TimelineUi() {
  const timelineRef = useRef(null);
  const [searchParams, setSearchParams] = useSearchParams();

  const {theme} = useOutletContext();

  useEffect(() => {
    const container = timelineRef.current;

    const items = new DataSet([]);

    const options = {
      editable: false,
      align: "center",
      showWeekScale: true,
      start: dayjs().startOf("day").toDate(),
      end: dayjs().endOf("day").toDate(),
    };

    const timeline = new Timeline(container, items, options);
    timeline.addCustomTime(dayjs().toDate(), "id");
    timeline.on("timechange", (e) =>
      setSearchParams({ time: dayjs(e.time).unix() })
    );

    return () => timeline.destroy(); // clean up
  }, []);
  return (
    <div>
      <div
        ref={timelineRef}
        style={{
          height: "auto",
          borderTop: `1px solid ${ theme === "light" ? Colors.LIGHT_GRAY1}`,
        }}
      />
    </div>
  );
}
