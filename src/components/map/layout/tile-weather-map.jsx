import { Layer, Source } from "react-map-gl/mapbox";

export default function TileWeatherMap({
  layer = "precipitation_new",
  opacity = 0.8,
  visible = true,
}) {
  if (!visible) return null;
  const url = `https://tile.openweathermap.org/map/${layer}/{z}/{x}/{y}.png?appid=${
    import.meta.env.VITE_WEATHER_APIKEY
  }`;

  return (
    <>
      <Source id="weather" type="raster" tiles={[url]}>
        <Layer
          id="weather"
          type="raster"
          paint={{
            "raster-opacity": opacity,
            "raster-fade-duration": 0,
          }}
        />
      </Source>
    </>
  );
}
