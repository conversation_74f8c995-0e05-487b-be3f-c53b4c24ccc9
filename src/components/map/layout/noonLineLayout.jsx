import { useEffect, useState } from "react";
import { Source, Layer } from "react-map-gl/mapbox";

// --- sous-solaire (longitude) ---
function subsolarLongitude(date = new Date()) {
  const toRad = (d) => (d * Math.PI) / 180,
    toDeg = (r) => (r * 180) / Math.PI;
  const JD = date.getTime() / 86400000 + 2440587.5;
  const T = (JD - 2451545.0) / 36525;
  const L = 280.46 + 36000.77 * T;
  const g = 357.528 + 35999.05 * T;
  const lambda = L + 1.915 * Math.sin(toRad(g)) + 0.02 * Math.sin(toRad(2 * g));
  const epsilon = 23.439 - 0.013 * T;
  const alpha = toDeg(
    Math.atan2(
      Math.cos(toRad(epsilon)) * Math.sin(toRad(lambda)),
      Math.cos(toRad(lambda))
    )
  );
  const JD0 = Math.floor(JD - 0.5) + 0.5,
    H = (JD - JD0) * 24,
    D = JD0 - 2451545.0;
  const GMST = (280.46061837 + 360.98564736629 * (D + H / 24)) % 360;
  const GHA = (GMST - (((alpha % 360) + 360) % 360) + 360) % 360;
  let lon = -GHA;
  if (lon < -180) lon += 360;
  if (lon >= 180) lon -= 360;
  return lon;
}

// --- anneau 12:00–13:00 (lonNoon-15° -> lonNoon), gestion anti-méridien ---
function bandGeoJSON(lonNoon) {
  const wrap = (x) => ((((x + 180) % 360) + 360) % 360) - 180;
  const lon13 = wrap(lonNoon - 15);
  const makePoly = (x1, x2) => ({
    type: "Polygon",
    coordinates: [
      [
        [x1, -90],
        [x2, -90],
        [x2, 90],
        [x1, 90],
        [x1, -90],
      ],
    ],
  });

  // pas de franchissement
  if (lon13 <= lonNoon && lonNoon - lon13 <= 180) {
    return { type: "Feature", geometry: makePoly(lon13, lonNoon) };
  }
  // franchit -180/180 → deux polygones
  const left = makePoly(lon13, 180);
  const right = makePoly(-180, lonNoon);
  return {
    type: "Feature",
    geometry: {
      type: "MultiPolygon",
      coordinates: [left.coordinates, right.coordinates],
    },
  };
}

export default function NoonLine() {
  const [geo, setGeo] = useState(bandGeoJSON(subsolarLongitude()));

  useEffect(() => {
    const tick = () => setGeo(bandGeoJSON(subsolarLongitude()));
    tick();
    const id = setInterval(tick, 60_000); // mise à jour chaque minute
    return () => clearInterval(id);
  }, []);

  return (
    <Source id="noon-12-13-band" type="geojson" data={geo}>
      <Layer
        id="noon-12-13-fill"
        type="fill"
        paint={{ "fill-color": "#ffd000", "fill-opacity": 0.1 }}
      />
      <Layer
        id="noon-12-13-outline"
        type="line"
        paint={{ "line-color": "#ffd000", "line-width": 0.5 }}
      />
    </Source>
  );
}
