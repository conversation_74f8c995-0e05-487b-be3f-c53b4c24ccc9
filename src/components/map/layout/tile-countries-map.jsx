import { Layer, Source } from "react-map-gl/mapbox";

export default function TileCountriesMap({
  countries = ["FRA"],
  visible = true,
}) {
  if (!visible) return null;
  return (
    <Source
      id="countries"
      type="vector"
      url="mapbox://mapbox.country-boundaries-v1"
    >
      <Layer
        id="countries-fill"
        type="fill"
        source="countries"
        source-layer="country_boundaries"
        // filter={["in", "iso_3166_1_alpha_3", ...countries]}
        paint={{
          "fill-color": "#af007c",
          "fill-opacity": 0.1,
        }}
      />
      <Layer
        id="countries-outline"
        type="line"
        source="countries"
        source-layer="country_boundaries"
        //filter={["in", "iso_3166_1_alpha_3", ...countries]}
        paint={{
          "line-color": "#af007c",
          "line-width": 0.5,
        }}
      />
    </Source>
  );
}
