import { Colors } from "@blueprintjs/core";
import { Layer, Source } from "react-map-gl/mapbox";

export default function TileTraficMap({ visible = true }) {
  if (!visible) return null;
  return (
    <Source id="traffic" type="vector" url="mapbox://mapbox.mapbox-traffic-v1">
      <Layer
        id="traffic-layer"
        type="line"
        source="traffic"
        source-layer="traffic"
        paint={{
          "line-color": [
            "match",
            ["get", "congestion"],
            "low",
            Colors.GREEN3,
            "moderate",
            Colors.GOLD3,
            "heavy",
            Colors.ORANGE3,
            "severe",
            Colors.RED3,
            "#999",
          ],
          "line-width": 2,
          "line-opacity": 0.5,
        }}
      />
    </Source>
  );
}
