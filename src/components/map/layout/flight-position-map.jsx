import { Colors, Entity<PERSON><PERSON>le, Icon, Popover, Tooltip } from "@blueprintjs/core";
import { <PERSON><PERSON>, <PERSON><PERSON>, Source } from "react-map-gl/mapbox";
import { useOutletContext } from "react-router";
import { useState, useEffect } from "react";

export default function FlightPositionMap({
  visible = true,
  company = ["FDX"],
  all = false,
  mapRef,
}) {
  const { flights, theme } = useOutletContext();
  const [mapBearing, setMapBearing] = useState(0);

  useEffect(() => {
    if (mapRef?.current) {
      const updateBearing = () => {
        setMapBearing(mapRef.current.getBearing());
      };

      mapRef.current.on("rotate", updateBearing);
      updateBearing(); // Initial bearing

      return () => {
        mapRef.current?.off("rotate", updateBearing);
      };
    }
  }, [mapRef]);

  return (
    <>
      <Source
        id="streets"
        type="vector"
        url="mapbox://mapbox.mapbox-streets-v8"
      >
        {/* Emprise aéroport */}
        <Layer
          id="airport-fill"
          type="fill"
          source-layer="landuse"
          filter={["==", ["get", "class"], "airport"]}
          paint={{ "fill-color": "#00FF88", "fill-opacity": 0.35 }}
        />
      </Source>

      {visible &&
        flights?.states
          ?.filter((f) => company.includes(f[1].slice(0, 3)))
          .map((f) => (
            <Marker
              longitude={f[5]}
              latitude={f[6]}
              key={f[0]}
              rotation={-45 + f[10] - mapBearing}
              altitude={f[13]}
            >
              <Popover
                content={
                  <>
                    <div
                      style={{
                        paddingInline: 10,
                        minWidth: 200,
                        paddingBlock: "10px 5px",
                        borderBottom: `1px solid ${
                          theme === "light"
                            ? Colors.LIGHT_GRAY1
                            : Colors.DARK_GRAY5
                        }`,
                      }}
                    >
                      <EntityTitle title={f[1]} subtitle={f[0]} />
                    </div>
                    <div
                      style={{
                        paddingInline: 10,
                        minWidth: 200,
                        fontSize: 12,
                        fontFamily: "monospace",
                        paddingBlock: "10px 10px",
                      }}
                    >
                      <EntityTitle fill title={"ORIGIN"} tags={f[2]} />
                      <EntityTitle fill title={"TIME POSITION"} tags={f[3]} />
                      <EntityTitle
                        fill
                        title={"SPEED"}
                        tags={`${(f[9] * 1.852).toFixed(2)} km/h`}
                      />
                      <EntityTitle
                        fill
                        title={"ALTITUDE"}
                        tags={`${f[13]} m`}
                      />
                      <EntityTitle fill title={"HEADING"} tags={`${f[10]}°`} />
                      <EntityTitle
                        fill
                        title={"VERTICAL SPEED"}
                        tags={`${f[14]} m/s`}
                      />
                    </div>
                  </>
                }
              >
                <Icon
                  style={{ cursor: "pointer" }}
                  icon="airplane"
                  color={Colors.BLUE3}
                />
              </Popover>
            </Marker>
          ))}
      {all && flights?.states?.length > 0 && (
        <Source
          id="flights"
          type="geojson"
          data={{
            type: "FeatureCollection",
            features: flights.states.map((f) => ({
              type: "Feature",
              geometry: {
                type: "Point",
                coordinates: [f[5], f[6]],
                altitude: f[13],
                heading: f[10],
              },
              properties: {
                callsign: f[1],
                heading: f[10],
                altitude: f[13],
              },
            })),
          }}
        >
          <Layer
            id="flights"
            type="circle"
            paint={{
              "circle-color": Colors.BLUE5,
              "circle-radius": 3,
              "circle-opacity": 0.5,
            }}
          />
        </Source>
      )}
    </>
  );
}
