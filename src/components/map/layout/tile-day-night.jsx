import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { Layer, Source } from "react-map-gl/mapbox";
import * as SunCalc from "suncalc";

// fonction pour générer le polygone nuit
function getNightPolygon(date, steps = 100) {
  // On génère des points de longitudes de -180 à +180
  // pour chaque longitude, on calcule la latitude du terminator
  // basé sur l’angle d’illumination du soleil via SunCalc

  const sunPos = { lat: null, lng: null }; // on n’utilise pas directement ici

  // On génère un tableau de points
  const coords = [];

  for (let i = 0; i <= steps; i++) {
    const lon = -180 + (i * 360) / steps;

    // Pour chaque lon, on cherche la latitude du terminator
    // On fait ceci en cherchant une latitude telle que la position
    // du soleil sur ce point a une altitude = 0 (juste lever/coucher)
    // But: trouver lat such that SunCalc.getPosition(date, lat, lon).altitude ≈ 0

    // On peut faire une recherche simple sur lat de -90 à +90
    let latLow = -90,
      latHigh = 90,
      latMid,
      alt;
    for (let j = 0; j < 20; j++) {
      // itérations binaire
      latMid = (latLow + latHigh) / 2;
      alt = SunCalc.getPosition(date, latMid, lon).altitude;
      if (alt > 0) {
        // point est dans jour => déplacer borne supérieure
        latLow = latMid;
      } else {
        latHigh = latMid;
      }
    }
    coords.push([lon, latMid]);
  }

  // Ajouter l’autre moitié pour fermer le polygone (lat = -90 -> +90)
  // On forme d’abord le contour de la terminator, puis on rejoint le pôle sud
  const polygon = {
    type: "Feature",
    geometry: {
      type: "Polygon",
      coordinates: [
        coords.concat([
          [180, 90],
          [-180, 90],
        ]),
      ],
    },
  };

  return {
    type: "FeatureCollection",
    features: [polygon],
  };
}

export default function TileDayNight({
  visible = true,
  date = dayjs().toDate(),
}) {
  const [nightGeoJson, setNightGeoJson] = useState(null);

  useEffect(() => {
    let mounted = true;

    const update = () => {
      const now = date;
      const geo = getNightPolygon(now, 200); // 200 steps pour meilleure finesse
      if (mounted) setNightGeoJson(geo);
    };

    update();
    const id = setInterval(update, 60_000);
    return () => {
      mounted = false;
      clearInterval(id);
    };
  }, [date]);
  if (!visible) return null;

  return (
    <>
      {nightGeoJson && (
        <Source id="night" type="geojson" data={nightGeoJson}>
          <Layer
            id="night-layer"
            type="fill"
            paint={{
              "fill-color": "#000",
              "fill-opacity": 0.2,
              "fill-emissive-strength": 0.5,
            }}
          />
        </Source>
      )}
    </>
  );
}
