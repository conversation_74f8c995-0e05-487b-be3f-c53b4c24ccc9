import {
  <PERSON>ton,
  ButtonGroup,
  EntityTitle,
  Icon,
  Menu,
  MenuDivider,
  MenuItem,
  Popover,
  Slider,
  Switch,
  Tooltip,
} from "@blueprintjs/core";
import TileWeatherMap from "../layout/tile-weather-map";
import { useEffect, useState } from "react";
import TileReliefMap from "../layout/tile-relief-map";
import TileTraficMap from "../layout/tile-trafic-map";
import TileCountriesMap from "../layout/tile-countries-map";
import FlightPositionMap from "../layout/flight-position-map";
import { Marker } from "react-map-gl/mapbox";

export default function BasicControlMap({ mapRef }) {
  const [weatherLayer, setWeatherLayer] = useState(null);
  const [weatherOpacity, setWeatherOpacity] = useState(0.8);
  const [traficVisible, setTraficVisible] = useState(false);
  const [userPosition, setUserPosition] = useState(null);

  useEffect(() => {
    if (userPosition) {
      mapRef.current.flyTo({
        center: [userPosition.longitude, userPosition.latitude],
        zoom: 10,
      });
    }
  }, [userPosition]);
  return (
    <>
      <div
        style={{
          position: "absolute",
          top: 15,
          right: 15,
          display: "flex",
          flexFlow: "column",
          gap: 15,
        }}
      >
        <Button
          icon="compass"
          onClick={() => mapRef.current.resetNorth({ bearing: 0, pitch: 0 })}
        />
        <Button
          intent={userPosition ? "primary" : "none"}
          icon="locate"
          onClick={() => {
            if (!userPosition) {
              navigator.geolocation.getCurrentPosition((position) => {
                setUserPosition(position.coords);
              });
            } else {
              setUserPosition(null);
            }
          }}
        />
        <ButtonGroup vertical>
          <Button icon="plus" onClick={() => mapRef.current.zoomIn()} />
          <Button icon="minus" onClick={() => mapRef.current.zoomOut()} />
        </ButtonGroup>
        <ButtonGroup vertical>
          <Tooltip content={"Show traffic"} compact>
            <Button
              icon="drive-time"
              intent={traficVisible ? "primary" : "none"}
              onClick={() => setTraficVisible(!traficVisible)}
            />
          </Tooltip>

          <Popover
            content={
              <Menu>
                <MenuDivider
                  title={
                    <EntityTitle
                      title="WEATHER LAYER"
                      icon="cloud"
                      subtitle={"From OpenWeatherMap"}
                    />
                  }
                />
                <MenuDivider />
                <MenuItem
                  active={weatherLayer === "precipitation_new"}
                  icon="rain"
                  text="Precipitation"
                  onClick={() => {
                    weatherLayer === "precipitation_new"
                      ? setWeatherLayer(null)
                      : setWeatherLayer("precipitation_new");
                  }}
                />
                <MenuItem
                  active={weatherLayer === "temp_new"}
                  icon="temperature"
                  text="Temperature"
                  onClick={() => {
                    weatherLayer === "temp_new"
                      ? setWeatherLayer(null)
                      : setWeatherLayer("temp_new");
                  }}
                />
                <MenuItem
                  active={weatherLayer === "clouds_new"}
                  icon="cloud"
                  text="Clouds"
                  onClick={() => {
                    weatherLayer === "clouds_new"
                      ? setWeatherLayer(null)
                      : setWeatherLayer("clouds_new");
                  }}
                />
                <MenuItem
                  active={weatherLayer === "wind_new"}
                  icon="wind"
                  text="Wind speed"
                  onClick={() =>
                    weatherLayer === "wind_new"
                      ? setWeatherLayer(null)
                      : setWeatherLayer("wind_new")
                  }
                />
                <div style={{ padding: "10px 15px" }}>
                  <Slider
                    value={weatherOpacity}
                    onChange={setWeatherOpacity}
                    min={0}
                    max={1}
                    stepSize={0.1}
                  />
                </div>
              </Menu>
            }
          >
            <Tooltip content={"Show weather layer"} compact>
              <Button icon="cloud" intent={weatherLayer ? "primary" : "none"} />
            </Tooltip>
          </Popover>
        </ButtonGroup>
      </div>
      <TileWeatherMap
        layer={weatherLayer}
        opacity={weatherOpacity}
        visible={!!weatherLayer}
      />
      <TileReliefMap />
      <TileTraficMap visible={traficVisible} />
      {userPosition && (
        <Marker
          longitude={userPosition.longitude}
          latitude={userPosition.latitude}
        >
          <Icon icon="locate" />
        </Marker>
      )}
    </>
  );
}
