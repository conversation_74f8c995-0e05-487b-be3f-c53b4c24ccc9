body ::selection {
  background: #af007c;
  color: white;
}

.sidebarMenu .bp6-menu-item {
  padding-inline: 17px;
  height: 35px;
  display: flex;
  align-items: center;
  font-weight: 500;
  gap: 5px;
}

.sidebarMenu .bp6-intent-primary {
  color: #af007c !important;
}

.sidebarMenu .bp6-intent-primary:hover {
  background-color: #af007c10 !important;
}

.sidebarMenu .bp6-intent-primary:active {
  background-color: #af007c20 !important;
}

.vis-current-time {
  background-color: #af007c !important;
}

.vis-timeline {
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  cursor: e-resize !important;
}

.vis-time-axis .vis-text {
  font-family: monospace !important;
}

.bp6-dark .vis-time-axis .vis-text {
  color: #fff !important;
}
