{"name": "fmaip-calcite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@blueprintjs/core": "^6.3.1", "@blueprintjs/datetime": "^6.0.5", "@turf/turf": "^7.2.0", "@types/mapbox-gl": "^3.4.1", "dayjs": "^1.11.18", "firebase": "^12.2.1", "mapbox-gl": "^3.15.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-map-gl": "^8.0.4", "react-router": "^7.9.1", "suncalc": "^1.9.0", "tz-lookup": "^6.1.25", "vis-timeline": "^8.3.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^4.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}